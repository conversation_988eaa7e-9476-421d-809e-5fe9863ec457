#!/usr/bin/env python3
"""
Standardized Error Handling Utilities for TNGD Backup System

This module provides consistent error handling patterns, logging,
and recovery mechanisms across the entire codebase.
"""

import logging
import traceback
import functools
from typing import Any, Callable, Dict, List, Optional, Type, Union
from ..constants import ErrorConstants


class BackupError(Exception):
    """Base exception for all backup-related errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}


class ApiError(BackupError):
    """API-related errors."""
    pass


class NetworkError(BackupError):
    """Network-related errors."""
    pass


class ConfigurationError(BackupError):
    """Configuration-related errors."""
    pass


class ResourceError(BackupError):
    """Resource exhaustion errors."""
    pass


class ValidationError(BackupError):
    """Data validation errors."""
    pass


class ErrorHandler:
    """Centralized error handling utility."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
    
    def is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error is retryable."""
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        # Check against retryable error types
        if error_type in ErrorConstants.RETRYABLE_ERRORS:
            return True
        
        # Check against non-retryable error types
        if error_type in ErrorConstants.NON_RETRYABLE_ERRORS:
            return False
        
        # Check error message for retryable patterns
        retryable_patterns = [
            'timeout', 'connection', 'network', 'temporary',
            'service unavailable', 'too many requests', 'rate limit'
        ]
        
        for pattern in retryable_patterns:
            if pattern in error_message:
                return True
        
        # Check for non-retryable patterns
        non_retryable_patterns = [
            'authentication', 'permission', 'unauthorized',
            'forbidden', 'not found', 'invalid', 'malformed'
        ]
        
        for pattern in non_retryable_patterns:
            if pattern in error_message:
                return False
        
        # Default to retryable for unknown errors
        return True
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None,
                  level: int = logging.ERROR) -> None:
        """Log an error with consistent formatting."""
        error_type = type(error).__name__
        error_message = str(error)
        
        # Build log message
        log_parts = [f"{error_type}: {error_message}"]
        
        # Add context if provided
        if context:
            context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
            log_parts.append(f"Context: {context_str}")
        
        # Add stack trace for ERROR level and above
        if level >= logging.ERROR:
            log_parts.append(f"Stack trace: {traceback.format_exc()}")
        
        self.logger.log(level, " | ".join(log_parts))
    
    def handle_error(self, error: Exception, operation: str,
                    context: Optional[Dict[str, Any]] = None,
                    reraise: bool = True) -> None:
        """Handle an error with consistent logging and optional re-raising."""
        enhanced_context = {"operation": operation}
        if context:
            enhanced_context.update(context)
        
        self.log_error(error, enhanced_context)
        
        if reraise:
            raise
    
    def create_error_result(self, error: Exception, operation: str,
                           context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a standardized error result dictionary."""
        return {
            "status": "error",
            "error": str(error),
            "error_type": type(error).__name__,
            "operation": operation,
            "retryable": self.is_retryable_error(error),
            "context": context or {},
            "timestamp": None  # Will be set by caller if needed
        }


def with_error_handling(operation_name: str, logger: Optional[logging.Logger] = None,
                       reraise: bool = True, return_on_error: Any = None):
    """Decorator for consistent error handling."""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            error_handler = ErrorHandler(logger)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys())
                }
                
                error_handler.handle_error(e, operation_name, context, reraise=False)
                
                if reraise:
                    raise
                else:
                    return return_on_error
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return: Any = None,
                logger: Optional[logging.Logger] = None, **kwargs) -> Any:
    """Safely execute a function with error handling."""
    error_handler = ErrorHandler(logger)
    
    try:
        return func(*args, **kwargs)
    except Exception as e:
        context = {
            "function": func.__name__ if hasattr(func, '__name__') else str(func),
            "args": str(args)[:100],  # Truncate long args
            "kwargs": str(kwargs)[:100]  # Truncate long kwargs
        }
        
        error_handler.log_error(e, context, level=logging.WARNING)
        return default_return


# Pre-configured error handlers for common operations
def get_api_error_handler(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """Get error handler configured for API operations."""
    return ErrorHandler(logger)


def get_storage_error_handler(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """Get error handler configured for storage operations."""
    return ErrorHandler(logger)


def get_processing_error_handler(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """Get error handler configured for data processing operations."""
    return ErrorHandler(logger)
